# 工具API接口测试文档

本文档包含所有API接口的请求参数示例和响应格式示例，按照接口复杂度分为三个阶段。

## 统一响应格式

所有API接口遵循统一的响应格式：

### 成功响应格式
```json
{
    "code": 200,                    // 业务状态码（成功）
    "message": "操作成功",           // 响应消息
    "data": {                       // 响应数据
        // 具体业务数据
    },
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

### 错误响应格式
```json
{
    "code": 1006,                   // 业务错误码
    "message": "积分不足",           // 错误消息
    "data": null,                   // 错误数据（可选）
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

## 认证机制

API支持两种认证方式：

1. **Bearer Token方式**（推荐）：
   ```
   Authorization: Bearer {token}
   ```

2. **URL参数方式**（兼容性）：
   ```
   ?token={token}
   ```

## 第一阶段：无或轻度数据依赖接口

### AdController (2个接口)

#### 1.1 广告开始 `POST /api/ad/store`

**请求参数示例**：
```json
{
    "ad_type": "banner",
    "position": "home_top",
    "duration": 30,
    "target_audience": {
        "age_range": "18-35",
        "interests": ["ai", "technology"]
    },
    "device_info": {
        "device_id": "d123456789",
        "platform": "ios",
        "os_version": "15.0"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告记录创建成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "estimated_end_time": "2023-01-01 12:00:30",
        "status": "active"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数验证失败**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "ad_type": ["广告类型不能为空"],
            "position": ["广告位置不能为空"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1.2 广告结束 `POST /api/ad/update`

**请求参数示例**：
```json
{
    "ad_id": 12345,
    "end_time": "2023-01-01 12:00:30",
    "interaction_data": {
        "clicks": 5,
        "impressions": 100,
        "completion_rate": 0.85,
        "engagement_time": 25
    },
    "user_feedback": {
        "rating": 4,
        "comments": "内容相关性高"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告数据更新成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "end_time": "2023-01-01 12:00:30",
        "status": "completed",
        "performance": {
            "clicks": 5,
            "impressions": 100,
            "ctr": 0.05,
            "completion_rate": 0.85
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 广告不存在**：
```json
{
    "code": 404,
    "message": "广告不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AiModelController (5个接口)

#### 2.1 获取可用模型 `GET /api/ai-models/available`

**请求参数示例**：
```json
{
    "type": "text",
    "capability": "generation",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "GPT-4",
                "type": "text",
                "capability": ["generation", "completion", "chat"],
                "provider": "openai",
                "status": "available",
                "description": "最新的GPT-4模型",
                "max_tokens": 8192,
                "cost_per_token": 0.03
            },
            {
                "id": 2,
                "name": "Claude-3",
                "type": "text",
                "capability": ["generation", "analysis"],
                "provider": "anthropic",
                "status": "available",
                "description": "Claude-3 Sonnet模型",
                "max_tokens": 4096,
                "cost_per_token": 0.02
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 15,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.2 获取模型详情 `GET /api/ai-models/{model_id}/detail`

**请求参数示例**：
```json
{
    "model_id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "GPT-4",
        "type": "text",
        "capability": ["generation", "completion", "chat"],
        "provider": "openai",
        "status": "available",
        "description": "最新的GPT-4模型，具有强大的文本生成和理解能力",
        "max_tokens": 8192,
        "cost_per_token": 0.03,
        "parameters": {
            "temperature": {
                "min": 0.0,
                "max": 2.0,
                "default": 1.0
            },
            "top_p": {
                "min": 0.0,
                "max": 1.0,
                "default": 1.0
            }
        },
        "supported_formats": ["text", "json"],
        "rate_limits": {
            "requests_per_minute": 60,
            "tokens_per_minute": 40000
        },
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.3 获取收藏模型 `GET /api/ai-models/favorites`

**请求参数示例**：
```json
{
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "GPT-4",
                "type": "text",
                "provider": "openai",
                "status": "available",
                "favorited_at": "2023-01-01 12:00:00",
                "usage_count": 25
            },
            {
                "id": 3,
                "name": "DALL-E 3",
                "type": "image",
                "provider": "openai",
                "status": "available",
                "favorited_at": "2023-01-02 10:30:00",
                "usage_count": 12
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 5,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.4 模型列表 `GET /api/ai-models/list`

**请求参数示例**：
```json
{
    "category": "text",
    "sort": "popularity",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "GPT-4",
                "category": "text",
                "provider": "openai",
                "status": "available",
                "popularity_score": 95,
                "usage_count": 1250,
                "rating": 4.8,
                "description": "最新的GPT-4模型"
            },
            {
                "id": 2,
                "name": "Claude-3",
                "category": "text",
                "provider": "anthropic",
                "status": "available",
                "popularity_score": 88,
                "usage_count": 890,
                "rating": 4.6,
                "description": "Claude-3 Sonnet模型"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 25,
            "last_page": 2
        },
        "categories": ["text", "image", "audio", "video"],
        "sort_options": ["popularity", "rating", "newest", "name"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.5 切换模型 `POST /api/ai-models/switch`

**请求参数示例**：
```json
{
    "model_id": 2,
    "task_type": "text_generation",
    "config": {
        "temperature": 0.8,
        "max_tokens": 2048,
        "top_p": 0.9
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "模型切换成功",
    "data": {
        "current_model": {
            "id": 2,
            "name": "Claude-3",
            "type": "text",
            "provider": "anthropic",
            "status": "active"
        },
        "previous_model": {
            "id": 1,
            "name": "GPT-4",
            "type": "text",
            "provider": "openai"
        },
        "switch_time": "2023-01-01 12:00:00",
        "config_applied": {
            "temperature": 0.8,
            "max_tokens": 2048,
            "top_p": 0.9
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不可用**：
```json
{
    "code": 1013,
    "message": "模型不可用",
    "data": {
        "model_id": 2,
        "reason": "模型正在维护中",
        "estimated_available_time": "2023-01-01 14:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AssetController (3个接口)

#### 3.1 获取素材列表 `GET /api/assets/list`

**请求参数示例**：
```json
{
    "type": "image",
    "category": "background",
    "search": "风景",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "山水风景.jpg",
                "type": "image",
                "category": "background",
                "url": "https://example.com/assets/landscape1.jpg",
                "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
                "size": 2048576,
                "dimensions": {
                    "width": 1920,
                    "height": 1080
                },
                "tags": ["风景", "山水", "自然"],
                "created_at": "2023-01-01 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 150,
            "last_page": 8
        },
        "filters": {
            "types": ["image", "video", "audio"],
            "categories": ["background", "character", "effect"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.2 获取素材详情 `GET /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "山水风景.jpg",
        "type": "image",
        "category": "background",
        "url": "https://example.com/assets/landscape1.jpg",
        "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
        "size": 2048576,
        "format": "jpeg",
        "dimensions": {
            "width": 1920,
            "height": 1080
        },
        "metadata": {
            "color_palette": ["#2E8B57", "#87CEEB", "#F5DEB3"],
            "dominant_colors": ["green", "blue", "beige"],
            "style": "realistic"
        },
        "tags": ["风景", "山水", "自然", "宁静"],
        "usage_count": 45,
        "download_count": 23,
        "rating": 4.7,
        "license": "commercial",
        "author": "AI Generated",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.3 删除素材 `DELETE /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "素材删除成功",
    "data": {
        "deleted_asset": {
            "id": 1,
            "name": "山水风景.jpg",
            "type": "image"
        },
        "deleted_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 无权限删除**：
```json
{
    "code": 403,
    "message": "无权限删除该素材",
    "data": {
        "reason": "只能删除自己上传的素材"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```
