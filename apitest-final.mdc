# 工具API接口测试文档

本文档包含所有API接口的请求参数示例和响应格式示例，按照接口复杂度分为三个阶段。

## 统一响应格式

所有API接口遵循统一的响应格式：

### 成功响应格式
```json
{
    "code": 200,                    // 业务状态码（成功）
    "message": "操作成功",           // 响应消息
    "data": {                       // 响应数据
        // 具体业务数据
    },
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

### 错误响应格式
```json
{
    "code": 1006,                   // 业务错误码
    "message": "积分不足",           // 错误消息
    "data": null,                   // 错误数据（可选）
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

## 认证机制

API支持两种认证方式：

1. **Bearer Token方式**（推荐）：
   ```
   Authorization: Bearer {token}
   ```

2. **URL参数方式**（兼容性）：
   ```
   ?token={token}
   ```

## 第一阶段：无或轻度数据依赖接口

### AdController (2个接口)

#### 1.1 广告开始 `POST /api/ad/store`

**请求参数示例**：
```json
{
    "ad_type": "banner",
    "position": "home_top",
    "duration": 30,
    "target_audience": {
        "age_range": "18-35",
        "interests": ["ai", "technology"]
    },
    "device_info": {
        "device_id": "d123456789",
        "platform": "ios",
        "os_version": "15.0"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告记录创建成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "estimated_end_time": "2023-01-01 12:00:30",
        "status": "active"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数验证失败**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "ad_type": ["广告类型不能为空"],
            "position": ["广告位置不能为空"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1.2 广告结束 `POST /api/ad/update`

**请求参数示例**：
```json
{
    "ad_id": 12345,
    "end_time": "2023-01-01 12:00:30",
    "interaction_data": {
        "clicks": 5,
        "impressions": 100,
        "completion_rate": 0.85,
        "engagement_time": 25
    },
    "user_feedback": {
        "rating": 4,
        "comments": "内容相关性高"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告数据更新成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "end_time": "2023-01-01 12:00:30",
        "status": "completed",
        "performance": {
            "clicks": 5,
            "impressions": 100,
            "ctr": 0.05,
            "completion_rate": 0.85
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 广告不存在**：
```json
{
    "code": 404,
    "message": "广告不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AiModelController (5个接口)

#### 2.1 获取可用模型 `GET /api/ai-models/available`

**请求参数示例**：
```json
{
    "type": "text",
    "capability": "generation",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "type": "text",
                "capability": ["generation", "completion", "chat"],
                "provider": "DeepSeek",
                "status": "available",
                "description": "强大的DeepSeek对话模型",
                "max_tokens": 8192,
                "cost_per_token": 0.02
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "type": "text",
                "capability": ["generation", "analysis"],
                "provider": "MiniMax",
                "status": "available",
                "description": "MiniMax多模态对话模型",
                "max_tokens": 4096,
                "cost_per_token": 0.015
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 15,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.2 获取模型详情 `GET /api/ai-models/{model_id}/detail`

**请求参数示例**：
```json
{
    "model_id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "DeepSeek Chat",
        "type": "text",
        "capability": ["generation", "completion", "chat"],
        "provider": "DeepSeek",
        "status": "available",
        "description": "强大的DeepSeek对话模型，具有优秀的文本生成和理解能力",
        "max_tokens": 8192,
        "cost_per_token": 0.02,
        "parameters": {
            "temperature": {
                "min": 0.0,
                "max": 2.0,
                "default": 1.0
            },
            "top_p": {
                "min": 0.0,
                "max": 1.0,
                "default": 1.0
            }
        },
        "supported_formats": ["text", "json"],
        "rate_limits": {
            "requests_per_minute": 60,
            "tokens_per_minute": 40000
        },
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.3 获取收藏模型 `GET /api/ai-models/favorites`

**请求参数示例**：
```json
{
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "type": "text",
                "provider": "DeepSeek",
                "status": "available",
                "favorited_at": "2023-01-01 12:00:00",
                "usage_count": 25
            },
            {
                "id": 3,
                "name": "LiblibAI 图像生成",
                "type": "image",
                "provider": "LiblibAI",
                "status": "available",
                "favorited_at": "2023-01-02 10:30:00",
                "usage_count": 12
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 5,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.4 模型列表 `GET /api/ai-models/list`

**请求参数示例**：
```json
{
    "category": "text",
    "sort": "popularity",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "category": "text",
                "provider": "DeepSeek",
                "status": "available",
                "popularity_score": 95,
                "usage_count": 1250,
                "rating": 4.8,
                "description": "强大的DeepSeek对话模型"
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "category": "text",
                "provider": "MiniMax",
                "status": "available",
                "popularity_score": 88,
                "usage_count": 890,
                "rating": 4.6,
                "description": "MiniMax多模态对话模型"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 25,
            "last_page": 2
        },
        "categories": ["text", "image", "audio", "video"],
        "sort_options": ["popularity", "rating", "newest", "name"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.5 切换模型 `POST /api/ai-models/switch`

**请求参数示例**：
```json
{
    "model_id": 2,
    "task_type": "text_generation",
    "config": {
        "temperature": 0.8,
        "max_tokens": 2048,
        "top_p": 0.9
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "模型切换成功",
    "data": {
        "current_model": {
            "id": 2,
            "name": "MiniMax Chat",
            "type": "text",
            "provider": "MiniMax",
            "status": "active"
        },
        "previous_model": {
            "id": 1,
            "name": "DeepSeek Chat",
            "type": "text",
            "provider": "DeepSeek"
        },
        "switch_time": "2023-01-01 12:00:00",
        "config_applied": {
            "temperature": 0.8,
            "max_tokens": 2048,
            "top_p": 0.9
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不可用**：
```json
{
    "code": 1013,
    "message": "模型不可用",
    "data": {
        "model_id": 2,
        "reason": "模型正在维护中",
        "estimated_available_time": "2023-01-01 14:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AssetController (3个接口)

#### 3.1 获取素材列表 `GET /api/assets/list`

**请求参数示例**：
```json
{
    "type": "image",
    "category": "background",
    "search": "风景",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "山水风景.jpg",
                "type": "image",
                "category": "background",
                "url": "https://example.com/assets/landscape1.jpg",
                "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
                "size": 2048576,
                "dimensions": {
                    "width": 1920,
                    "height": 1080
                },
                "tags": ["风景", "山水", "自然"],
                "created_at": "2023-01-01 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 150,
            "last_page": 8
        },
        "filters": {
            "types": ["image", "video", "audio"],
            "categories": ["background", "character", "effect"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.2 获取素材详情 `GET /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "山水风景.jpg",
        "type": "image",
        "category": "background",
        "url": "https://example.com/assets/landscape1.jpg",
        "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
        "size": 2048576,
        "format": "jpeg",
        "dimensions": {
            "width": 1920,
            "height": 1080
        },
        "metadata": {
            "color_palette": ["#2E8B57", "#87CEEB", "#F5DEB3"],
            "dominant_colors": ["green", "blue", "beige"],
            "style": "realistic"
        },
        "tags": ["风景", "山水", "自然", "宁静"],
        "usage_count": 45,
        "download_count": 23,
        "rating": 4.7,
        "license": "commercial",
        "author": "AI Generated",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.3 删除素材 `DELETE /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "素材删除成功",
    "data": {
        "deleted_asset": {
            "id": 1,
            "name": "山水风景.jpg",
            "type": "image"
        },
        "deleted_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 无权限删除**：
```json
{
    "code": 403,
    "message": "无权限删除该素材",
    "data": {
        "reason": "只能删除自己上传的素材"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AuthController (7个接口)

#### 4.1 用户注册 `POST /api/auth/register`

**请求参数示例**：
```json
{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "13800138000",
    "verification_code": "123456",
    "invite_code": "INV123456",
    "agree_terms": true
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user": {
            "id": 1001,
            "username": "testuser",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "avatar": "https://example.com/default-avatar.png",
            "status": "active",
            "created_at": "2023-01-01 12:00:00"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数验证失败**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "email": ["邮箱格式不正确"],
            "password": ["密码长度至少6位"],
            "verification_code": ["验证码错误"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 用户已存在**：
```json
{
    "code": 1002,
    "message": "用户已存在",
    "data": {
        "field": "email",
        "value": "<EMAIL>"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.2 用户登录 `POST /api/auth/login`

**请求参数示例**：
```json
{
    "login": "<EMAIL>",
    "password": "password123",
    "remember": true,
    "device_info": {
        "device_id": "d123456789",
        "platform": "web",
        "user_agent": "Mozilla/5.0..."
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1001,
            "username": "testuser",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar/1001.png",
            "vip_level": 1,
            "credits": 1000,
            "last_login": "2023-01-01 12:00:00"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00",
        "permissions": ["user.basic", "content.create"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 登录失败**：
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": {
        "attempts_remaining": 4,
        "lockout_time": null
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 账户被锁定**：
```json
{
    "code": 1003,
    "message": "账户已被锁定",
    "data": {
        "reason": "多次登录失败",
        "unlock_time": "2023-01-01 13:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.3 用户登出 `POST /api/auth/logout`

**请求参数示例**：
```json
{
    "all_devices": false
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "登出成功",
    "data": {
        "logout_time": "2023-01-01 12:00:00",
        "session_duration": "2小时30分钟"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.4 刷新Token `POST /api/auth/refresh`

**请求参数示例**：
```json
{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - Token无效**：
```json
{
    "code": 1004,
    "message": "Token无效或已过期",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.5 忘记密码 `POST /api/auth/forgot-password`

**请求参数示例**：
```json
{
    "email": "<EMAIL>",
    "captcha": "ABCD1234"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "重置密码邮件已发送",
    "data": {
        "email": "<EMAIL>",
        "expires_at": "2023-01-01 13:00:00",
        "reset_token_length": 32
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 用户不存在**：
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.6 重置密码 `POST /api/auth/reset-password`

**请求参数示例**：
```json
{
    "token": "abc123def456ghi789jkl012",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": {
        "reset_time": "2023-01-01 12:00:00",
        "auto_login": false
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 重置Token无效**：
```json
{
    "code": 1005,
    "message": "重置链接无效或已过期",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.7 验证Token `GET /api/auth/verify`

**请求参数示例**：
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "Token验证成功",
    "data": {
        "valid": true,
        "user_id": 1001,
        "expires_at": "2023-01-08 12:00:00",
        "permissions": ["user.basic", "content.create"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - Token无效**：
```json
{
    "code": 1004,
    "message": "Token无效或已过期",
    "data": {
        "valid": false,
        "reason": "expired"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### CaptchaController (3个接口)

#### 5.1 生成验证码 `GET /api/captcha/generate`

**请求参数示例**：
```json
{
    "type": "image",
    "width": 120,
    "height": 40,
    "length": 4,
    "difficulty": "medium"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码生成成功",
    "data": {
        "captcha_id": "cap_abc123def456",
        "image_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expires_at": "2023-01-01 12:05:00",
        "type": "image",
        "length": 4
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数错误**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "type": ["验证码类型不支持"],
            "length": ["长度必须在4-6之间"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 5.2 验证验证码 `POST /api/captcha/verify`

**请求参数示例**：
```json
{
    "captcha_id": "cap_abc123def456",
    "code": "ABCD",
    "case_sensitive": false
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码验证成功",
    "data": {
        "valid": true,
        "captcha_id": "cap_abc123def456",
        "verified_at": "2023-01-01 12:02:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码错误**：
```json
{
    "code": 1007,
    "message": "验证码错误",
    "data": {
        "valid": false,
        "attempts_remaining": 2,
        "captcha_id": "cap_abc123def456"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码过期**：
```json
{
    "code": 1008,
    "message": "验证码已过期",
    "data": {
        "expired_at": "2023-01-01 12:05:00",
        "current_time": "2023-01-01 12:06:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 5.3 刷新验证码 `POST /api/captcha/refresh`

**请求参数示例**：
```json
{
    "captcha_id": "cap_abc123def456",
    "type": "image"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码刷新成功",
    "data": {
        "captcha_id": "cap_def456ghi789",
        "image_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expires_at": "2023-01-01 12:10:00",
        "type": "image",
        "refreshed_at": "2023-01-01 12:05:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码不存在**：
```json
{
    "code": 404,
    "message": "验证码不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```
