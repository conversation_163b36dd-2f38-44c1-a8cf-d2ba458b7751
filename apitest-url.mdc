# 工具API接口服务

## 📊 AI模型配置信息

### 🤖 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 🚫 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 🎯 业务模型配置矩阵

#### 图像生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务 - 可选平台: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务 - 可选平台: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务 - 可选平台: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务 - 可选平台: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务 - 可选平台: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

## API接口列表：

### 无或轻度数据依赖 (约80个接口)

**AdController.php (2个接口)**
1.1 广告开始					POST /api/ad/store							AdController::ad_store()
1.2 广告结束					POST /api/ad/update							AdController::ad_update()

**AiModelController.php (5个接口)**
2.1 获取可用模型				GET /api/ai-models/available				AiModelController::available()
2.2 获取模型详情				GET /api/ai-models/{model_id}/detail		AiModelController::detail()
2.3 获取收藏模型				GET /api/ai-models/favorites				AiModelController::favorites()
2.4 模型列表					GET /api/ai-models/list						AiModelController::list()
2.5 切换模型					POST /api/ai-models/switch					AiModelController::switch()

**AssetController.php (3个接口)**
3.1 获取素材列表				GET /api/assets/list						AssetController::list()
3.2 获取素材详情				GET /api/assets/{id}						AssetController::show()
3.3 删除素材					DELETE /api/assets/{id}						AssetController::delete()

**AuthController.php (3个接口)**
4.1 用户注册					POST /api/register							AuthController::register()
4.2 用户登录					POST /api/login								AuthController::login()
4.3 检测token是否有效			GET /api/check								AuthController::check()

**CacheController.php (4个接口)**
5.1 获取缓存统计				GET /api/cache/stats						CacheController::getStats()
5.2 获取缓存键列表				GET /api/cache/keys							CacheController::getKeys()
5.3 获取缓存值				GET /api/cache/get							CacheController::getValue()
5.4 获取缓存配置				GET /api/cache/config						CacheController::getConfig()

**WebSocketController.php (4个接口)**
6.1 WebSocket连接认证			POST /api/websocket/auth					WebSocketController::authenticate()
6.2 获取WebSocket会话列表		GET /api/websocket/sessions					WebSocketController::getSessions()
6.3 断开WebSocket连接			POST /api/websocket/disconnect				WebSocketController::disconnect()
6.4 WebSocket服务状态			GET /api/websocket/status					WebSocketController::getStatus()

**SystemMonitorController.php (6个接口)**
7.1 系统健康检查				GET /api/system/health						SystemMonitorController::health()
7.2 性能指标监控				GET /api/system/metrics						SystemMonitorController::metrics()
7.3 响应时间监控				GET /api/system/response-time				SystemMonitorController::responseTime()
7.4 系统监控概览				GET /api/system/monitor/overview			SystemMonitorController::getMonitorOverview()
7.5 系统性能指标				GET /api/system/monitor/metrics				SystemMonitorController::getMetrics()
7.6 全局搜索					GET /api/system/search						SystemMonitorController::globalSearch()

**TaskManagementController.php (1个接口)**
8.1 获取超时配置				GET /api/tasks/timeout-config				TaskManagementController::getTimeoutConfig()

**StyleController.php (4个接口)**
9.1 获取剧情风格列表			GET /api/styles/list						StyleController::list()
9.2 获取风格详情				GET /api/styles/{id}						StyleController::detail()
9.3 获取热门风格				GET /api/styles/popular						StyleController::popular()
9.4 创建风格					POST /api/styles/create						StyleController::create()

**ApplicationMonitorController.php (6个接口)**
10.1 应用健康检查				GET /api/app-monitor/health					ApplicationMonitorController::health()
10.2 应用性能指标				GET /api/app-monitor/metrics				ApplicationMonitorController::metrics()
10.3 实时监控数据				GET /api/app-monitor/realtime				ApplicationMonitorController::realtime()
10.4 应用告警列表				GET /api/app-monitor/alerts					ApplicationMonitorController::alerts()
10.5 确认告警					PUT /api/app-monitor/alerts/{id}/acknowledge ApplicationMonitorController::acknowledgeAlert()
10.6 解决告警					PUT /api/app-monitor/alerts/{id}/resolve	ApplicationMonitorController::resolveAlert()

**VersionController.php (6个接口)**
11.1 创建资源版本				POST /api/resources/{id}/versions			VersionController::create()
11.2 获取版本历史				GET /api/resources/{id}/versions			VersionController::list()
11.3 获取版本详情				GET /api/versions/{id}						VersionController::show()
11.4 设置当前版本				PUT /api/versions/{id}/set-current			VersionController::setCurrent()
11.5 删除版本					DELETE /api/versions/{id}					VersionController::delete()
11.6 版本比较					GET /api/versions/compare					VersionController::compare()

### 中度数据依赖 (约120个接口)

**AiModelController.php (3个接口)**
12.1 测试模型					POST /api/ai-models/{model_id}/test		AiModelController::test()
12.2 获取使用统计				GET /api/ai-models/usage-stats				AiModelController::usageStats()
12.3 收藏模型					POST /api/ai-models/{model_id}/favorite		AiModelController::favorite()

**AiTaskController.php (6个接口)**
13.1 获取AI任务列表			GET /api/ai/tasks							AiTaskController::index()
13.2 获取AI任务详情			GET /api/ai/tasks/{id}						AiTaskController::show()
13.3 重试AI任务				POST /api/ai/tasks/{id}/retry				AiTaskController::retry()
13.4 取消AI任务				DELETE /api/ai/tasks/{id}					AiTaskController::cancel()
13.5 获取任务统计				GET /api/ai/tasks/stats						AiTaskController::stats()
13.6 创建AI任务				POST /api/ai/tasks							AiTaskController::create()

**AnalyticsController.php (6个接口)**
14.1 获取用户行为分析			GET /api/analytics/user-behavior			AnalyticsController::getUserBehavior()
14.2 获取系统使用统计			GET /api/analytics/system-usage				AnalyticsController::getSystemUsage()
14.3 获取AI平台性能分析		GET /api/analytics/ai-performance			AnalyticsController::getAiPerformance()
14.4 获取用户留存分析			GET /api/analytics/user-retention			AnalyticsController::getUserRetention()
14.5 获取收入分析				GET /api/analytics/revenue					AnalyticsController::getRevenue()
14.6 生成自定义报告			POST /api/analytics/custom-report			AnalyticsController::generateCustomReport()

**AssetController.php (1个接口)**
15.1 上传素材					POST /api/assets/upload						AssetController::upload()

**CacheController.php (4个接口)**
16.1 清理缓存					DELETE /api/cache/clear						CacheController::clearCache()
16.2 预热缓存					POST /api/cache/warmup						CacheController::warmupCache()
16.3 设置缓存值				PUT /api/cache/set							CacheController::setValue()
16.4 删除缓存键				DELETE /api/cache/delete					CacheController::deleteKeys()

**CharacterBindingController.php (5个接口)**
17.1 绑定角色					POST /api/characters/bind					CharacterBindingController::bind()
17.2 解绑角色					DELETE /api/characters/unbind				CharacterBindingController::unbind()
17.3 获取绑定列表				GET /api/characters/bindings				CharacterBindingController::list()
17.4 更新绑定					PUT /api/characters/bindings/{id}			CharacterBindingController::update()
17.5 获取绑定详情				GET /api/characters/bindings/{id}			CharacterBindingController::show()

**CharacterController.php (8个接口)**
18.1 角色分类列表				GET /api/characters/categories				CharacterController::getCategories()
18.2 角色列表					GET /api/characters/list					CharacterController::getLibrary()
18.3 获取角色详情				GET /api/characters/{id}					CharacterController::getCharacterDetail()
18.4 推荐角色					GET /api/characters/recommendations			CharacterController::getRecommendations()
18.5 角色绑定					POST /api/characters/bind					CharacterController::bindCharacter()
18.6 获取我的角色绑定			GET /api/characters/my-bindings				CharacterController::getMyBindings()
18.7 更新角色绑定				PUT /api/characters/bindings/{id}			CharacterController::updateBinding()
18.8 解绑角色					DELETE /api/characters/unbind				CharacterController::unbindCharacter()

**ConfigController.php (7个接口)**
19.1 获取系统配置				GET /api/config/system						ConfigController::getSystemConfig()
19.2 更新系统配置				PUT /api/config/system						ConfigController::updateSystemConfig()
19.3 获取用户配置				GET /api/config/user						ConfigController::getUserConfig()
19.4 更新用户配置				PUT /api/config/user						ConfigController::updateUserConfig()
19.5 获取AI配置				GET /api/config/ai							ConfigController::getAiConfig()
19.6 更新AI配置				PUT /api/config/ai							ConfigController::updateAiConfig()
19.7 重置配置					POST /api/config/reset						ConfigController::resetConfig()

**CreditsController.php (3个接口)**
20.1 积分预检查				POST /api/credits/check						CreditsController::checkCredits()
20.2 积分冻结					POST /api/credits/freeze					CreditsController::freezeCredits()
20.3 积分返还					POST /api/credits/refund					CreditsController::refundCredits()

**PointsController.php (3个接口)**
21.1 积分余额查询				GET /api/points/balance						PointsController::balance()
21.2 积分充值					POST /api/points/recharge					PointsController::recharge()
21.3 积分交易记录				GET /api/points/transactions				PointsController::transactions()

**NotificationController.php (6个接口)**
22.1 获取用户通知列表			GET /api/notifications						NotificationController::index()
22.2 标记通知为已读			PUT /api/notifications/mark-read			NotificationController::markAsRead()
22.3 标记所有通知为已读		PUT /api/notifications/mark-all-read		NotificationController::markAllAsRead()
22.4 删除通知					DELETE /api/notifications/{id}				NotificationController::destroy()
22.5 获取通知统计				GET /api/notifications/stats				NotificationController::stats()
22.6 发送系统通知				POST /api/notifications/send				NotificationController::send()

**PermissionController.php (7个接口)**
23.1 获取用户权限				GET /api/permissions/user					PermissionController::getUserPermissions()
23.2 检查用户权限				POST /api/permissions/check					PermissionController::checkPermission()
23.3 获取角色列表				GET /api/permissions/roles					PermissionController::getRoles()
23.4 分配用户角色				PUT /api/permissions/assign-role			PermissionController::assignRole()
23.5 授予用户权限				POST /api/permissions/grant					PermissionController::grantPermissions()
23.6 撤销用户权限				DELETE /api/permissions/revoke				PermissionController::revokePermissions()
23.7 获取权限历史				GET /api/permissions/history				PermissionController::getPermissionHistory()

**UserController.php (4个接口)**
24.1 用户中心信息				GET /api/user/profile						UserController::profile()
24.2 更新用户资料				PUT /api/user/profile						UserController::updateProfile()
24.3 用户偏好设置				PUT /api/user/preferences					UserController::updatePreferences()
24.4 获取用户偏好设置			GET /api/user/preferences					UserController::getPreferences()

**TemplateController.php (7个接口)**
25.1 创建模板					POST /api/templates/create					TemplateController::create()
25.2 使用模板					POST /api/templates/{id}/use				TemplateController::use()
25.3 模板市场					GET /api/templates/marketplace				TemplateController::marketplace()
25.4 我的模板					GET /api/templates/my-templates				TemplateController::myTemplates()
25.5 获取模板详情				GET /api/templates/{id}/detail				TemplateController::detail()
25.6 更新模板					PUT /api/templates/{id}						TemplateController::update()
25.7 删除模板					DELETE /api/templates/{id}					TemplateController::delete()

### 高度数据依赖 (约80个接口)

**TaskManagementController.php (4个接口)**
26.1 取消任务					POST /api/tasks/{id}/cancel					TaskManagementController::cancelTask()
26.2 重试任务					POST /api/tasks/{id}/retry					TaskManagementController::retryTask()
26.3 批量任务状态查询			GET /api/batch/tasks/status					TaskManagementController::getBatchStatus()
26.4 查询任务恢复状态			GET /api/tasks/{id}/recovery				TaskManagementController::getRecoveryStatus()

**CharacterController.php (1个接口)**
27.1 角色生成					POST /api/characters/generate				CharacterController::generate()

**StoryController.php (2个接口)**
28.1 故事生成					POST /api/stories/generate					StoryController::generate()
28.2 故事生成状态查询			GET /api/stories/{id}/status				StoryController::getStatus()

**AiGenerationController.php (4个接口)**
29.1 文本生成					POST /api/ai/text/generate					AiGenerationController::generateText()
29.2 获取生成任务状态			GET /api/ai/tasks/{id}						AiGenerationController::getTaskStatus()
29.3 获取用户生成任务列表		GET /api/ai/tasks							AiGenerationController::getUserTasks()
29.4 重试失败的任务			POST /api/ai/tasks/{id}/retry				AiGenerationController::retryTask()

**AudioController.php (4个接口)**
30.1 音频混音					POST /api/audio/mix							AudioController::mix()
30.2 音频混音状态查询			GET /api/audio/mix/{id}/status				AudioController::getMixStatus()
30.3 音频增强					POST /api/audio/enhance						AudioController::enhance()
30.4 音频增强状态查询			GET /api/audio/enhance/{id}/status			AudioController::getEnhanceStatus()

**BatchController.php (5个接口)**
31.1 批量图像生成				POST /api/batch/images/generate				BatchController::generateImages()
31.2 批量语音合成				POST /api/batch/voices/synthesize			BatchController::synthesizeVoices()
31.3 批量音乐生成				POST /api/batch/music/generate				BatchController::generateMusic()
31.4 获取批量任务状态			GET /api/batch/{batch_id}/status			BatchController::getBatchStatus()
31.5 取消批量任务				DELETE /api/batch/{batch_id}				BatchController::cancelBatch()

**DataExportController.php (4个接口)**
32.1 创建数据导出				POST /api/exports/create					DataExportController::createExport()
32.2 导出任务列表				GET /api/exports/list						DataExportController::getExports()
32.3 导出任务状态				GET /api/exports/{id}/status				DataExportController::getExportStatus()
32.4 下载导出文件				GET /api/exports/{id}/download				DataExportController::downloadExport()

**DownloadController.php (7个接口)**
33.1 下载历史列表				GET /api/downloads/list						DownloadController::list()
33.2 重试下载任务				POST /api/downloads/{id}/retry				DownloadController::retry()
33.3 获取下载统计				GET /api/downloads/statistics				DownloadController::statistics()
33.4 创建下载链接				POST /api/downloads/create-link				DownloadController::createLink()
33.5 安全下载					GET /api/downloads/secure/{token}			DownloadController::secureDownload()
33.6 批量下载					POST /api/downloads/batch					DownloadController::batchDownload()
33.7 清理过期下载				POST /api/downloads/cleanup					DownloadController::cleanup()

**GeneralExportController.php (7个接口)**
34.1 创建导出任务				POST /api/general-exports/create			GeneralExportController::create()
34.2 获取导出状态				GET /api/general-exports/{id}/status		GeneralExportController::getStatus()
34.3 下载导出文件				GET /api/general-exports/{id}/download		GeneralExportController::download()
34.4 导出任务列表				GET /api/general-exports/list				GeneralExportController::list()
34.5 取消导出任务				POST /api/general-exports/{id}/cancel		GeneralExportController::cancel()
34.6 删除导出任务				DELETE /api/general-exports/{id}			GeneralExportController::delete()
34.7 批量导出					POST /api/general-exports/batch			GeneralExportController::batchCreate()

**FileController.php (5个接口)**
35.1 文件上传					POST /api/files/upload						FileController::upload()
35.2 文件列表					GET /api/files/list							FileController::getFiles()
35.3 文件详情					GET /api/files/{id}							FileController::getFileDetail()
35.4 删除文件					DELETE /api/files/{id}						FileController::deleteFile()
35.5 文件下载					GET /api/files/{id}/download				FileController::downloadFile()

**ImageController.php (4个接口)**
36.1 图像生成					POST /api/images/generate					ImageController::generate()
36.2 图像生成状态查询			GET /api/images/{id}/status					ImageController::getStatus()
36.3 图像生成结果获取			GET /api/images/{id}/result					ImageController::getResult()
36.4 批量图像生成				POST /api/batch/images/generate				ImageController::batchGenerate()

**LogController.php (6个接口)**
37.1 查询系统日志				GET /api/logs/system						LogController::systemLogs()
37.2 查询用户操作日志			GET /api/logs/user-actions					LogController::userActionLogs()
37.3 查询AI调用日志			GET /api/logs/ai-calls						LogController::aiCallLogs()
37.4 查询错误日志				GET /api/logs/errors						LogController::errorLogs()
37.5 标记错误为已解决			PUT /api/logs/errors/{id}/resolve			LogController::resolveError()
37.6 导出日志					POST /api/logs/export						LogController::exportLogs()

**MusicController.php (4个接口)**
38.1 音乐生成					POST /api/music/generate					MusicController::generate()
38.2 音乐生成状态查询			GET /api/music/{id}/status					MusicController::getStatus()
38.3 音乐生成结果获取			GET /api/music/{id}/result					MusicController::getResult()
38.4 批量音乐生成				POST /api/batch/music/generate				MusicController::batchGenerate()

**ProjectController.php (8个接口)**
39.1 选风格+写剧情创建项目		POST /api/projects/create-with-story		ProjectController::createWithStory()
39.2 确认AI生成的项目标题		PUT /api/projects/{id}/confirm-title		ProjectController::confirmTitle()
39.3 获取用户项目列表			GET /api/projects/my-projects				ProjectController::myProjects()
39.4 获取项目详情				GET /api/projects/{id}						ProjectController::detail()
39.5 获取项目列表				GET /api/projects/list						ProjectController::list()
39.6 创建项目					POST /api/projects/create					ProjectController::create()
39.7 更新项目					PUT /api/projects/{id}						ProjectController::update()
39.8 删除项目					DELETE /api/projects/{id}					ProjectController::delete()

**ProjectManagementController.php (6个接口)**
40.1 创建项目					POST /api/projects/create					ProjectManagementController::create()
40.2 项目协作管理				POST /api/projects/{id}/collaboration		ProjectManagementController::manageCollaboration()
40.3 获取项目列表				GET /api/projects/list						ProjectManagementController::list()
40.4 获取项目详情				GET /api/projects/{id}/detail				ProjectManagementController::detail()
40.5 更新项目					PUT /api/projects/{id}						ProjectManagementController::update()
40.6 删除项目					DELETE /api/projects/{id}					ProjectManagementController::delete()

**PublicationController.php (9个接口)**
41.1 发布作品					POST /api/publications/publish				PublicationController::publish()
41.2 获取发布状态				GET /api/publications/{id}/status			PublicationController::getStatus()
41.3 更新作品信息				PUT /api/publications/{id}					PublicationController::update()
41.4 取消发布					DELETE /api/publications/{id}				PublicationController::delete()
41.5 取消发布(POST方式)		POST /api/publications/{id}/unpublish		PublicationController::unpublish()
41.6 我的发布列表				GET /api/publications/my-publications		PublicationController::myPublications()
41.7 作品广场					GET /api/publications/plaza					PublicationController::plaza()
41.8 获取作品详情				GET /api/publications/{id}/detail			PublicationController::detail()
41.9 热门作品					GET /api/publications/trending				PublicationController::trending()

**RecommendationController.php (8个接口)**
42.1 获取内容推荐				GET /api/recommendations/content			RecommendationController::content()
42.2 获取用户推荐				GET /api/recommendations/users				RecommendationController::users()
42.3 获取话题推荐				GET /api/recommendations/topics				RecommendationController::topics()
42.4 反馈推荐					POST /api/recommendations/feedback			RecommendationController::feedback()
42.5 获取推荐设置				GET /api/recommendations/preferences		RecommendationController::preferences()
42.6 更新推荐设置				PUT /api/recommendations/preferences		RecommendationController::updatePreferences()
42.7 获取推荐统计				GET /api/recommendations/analytics			RecommendationController::analytics()
42.8 个性化推荐				GET /api/recommendations/personalized		RecommendationController::personalized()

**ResourceController.php (9个接口)**
43.1 资源生成任务创建			POST /api/resources/generate				ResourceController::generate()
43.2 资源生成状态查询			GET /api/resources/{id}/status				ResourceController::getStatus()
43.3 资源列表查询				GET /api/resources/list						ResourceController::list()
43.4 删除资源					DELETE /api/resources/{id}					ResourceController::delete()
43.5 获取资源下载信息			GET /api/resources/{id}/download-info		ResourceController::getDownloadInfo()
43.6 确认下载完成				POST /api/resources/{id}/confirm-download	ResourceController::confirmDownload()
43.7 获取我的资源列表			GET /api/resources/my-resources				ResourceController::myResources()
43.8 更新资源状态				PUT /api/resources/{id}/status				ResourceController::updateStatus()
43.9 批量资源生成				POST /api/batch/resources/generate			ResourceController::batchGenerate()

**ReviewController.php (7个接口)**
44.1 提交审核					POST /api/reviews/submit					ReviewController::submit()
44.2 获取审核状态				GET /api/reviews/{id}/status				ReviewController::getStatus()
44.3 申请复审					POST /api/reviews/{id}/appeal				ReviewController::appeal()
44.4 我的审核记录				GET /api/reviews/my-reviews					ReviewController::myReviews()
44.5 审核队列状态				GET /api/reviews/queue-status				ReviewController::queueStatus()
44.6 审核指南					GET /api/reviews/guidelines					ReviewController::guidelines()
44.7 快速预检					POST /api/reviews/pre-check					ReviewController::preCheck()

**SocialController.php (10个接口)**
45.1 关注用户					POST /api/social/follow						SocialController::follow()
45.2 获取关注列表				GET /api/social/follows						SocialController::follows()
45.3 点赞内容					POST /api/social/like						SocialController::like()
45.4 评论内容					POST /api/social/comment					SocialController::comment()
45.5 获取评论列表				GET /api/social/comments					SocialController::comments()
45.6 分享内容					POST /api/social/share						SocialController::share()
45.7 获取社交动态				GET /api/social/feed						SocialController::feed()
45.8 获取通知					GET /api/social/notifications				SocialController::notifications()
45.9 标记通知已读				POST /api/social/mark-notifications-read	SocialController::markNotificationsRead()
45.10 获取社交统计			GET /api/social/stats						SocialController::getStats()

**SoundController.php (4个接口)**
46.1 音效生成					POST /api/sounds/generate					SoundController::generate()
46.2 音效生成状态查询			GET /api/sounds/{id}/status					SoundController::getStatus()
46.3 音效生成结果获取			GET /api/sounds/{id}/result					SoundController::getResult()
46.4 批量音效生成				POST /api/batch/sounds/generate				SoundController::batchGenerate()

**VideoController.php (3个接口)**
47.1 视频生成					POST /api/videos/generate					VideoController::generate()
47.2 视频生成状态查询			GET /api/videos/{id}/status					VideoController::getStatus()
47.3 视频生成结果获取			GET /api/videos/{id}/result					VideoController::getResult()

**VoiceController.php (8个接口)**
48.1 语音合成					POST /api/voices/synthesize					VoiceController::synthesize()
48.2 语音合成状态查询			GET /api/voices/{id}/status					VoiceController::getStatus()
48.3 批量语音合成				POST /api/batch/voices/synthesize			VoiceController::batchSynthesize()
48.4 音色克隆					POST /api/voices/clone						VoiceController::clone()
48.5 音色克隆状态查询			GET /api/voices/clone/{id}/status			VoiceController::getCloneStatus()
48.6 自定义音色生成			POST /api/voices/custom						VoiceController::custom()
48.7 自定义音色状态查询		GET /api/voices/custom/{id}/status			VoiceController::getCustomStatus()
48.8 音色试听					POST /api/voices/{id}/preview				VoiceController::preview()

**WorkPublishController.php (8个接口)**
49.1 发布作品					POST /api/works/publish						WorkPublishController::publishWork()
49.2 编辑作品					PUT /api/works/{id}							WorkPublishController::update()
49.3 删除作品					DELETE /api/works/{id}						WorkPublishController::delete()
49.4 获取我的作品				GET /api/works/my-works						WorkPublishController::myWorks()
49.5 作品展示库				GET /api/works/gallery						WorkPublishController::gallery()
49.6 获取分享链接				GET /api/works/{id}/share					WorkPublishController::getShareLink()
49.7 点赞作品					POST /api/works/{id}/like					WorkPublishController::like()
49.8 热门作品					GET /api/works/trending						WorkPublishController::trending()

**WorkflowController.php (8个接口)**
50.1 创建工作流				POST /api/workflows							WorkflowController::create()
50.2 获取工作流列表			GET /api/workflows							WorkflowController::index()
50.3 获取工作流详情			GET /api/workflows/{id}						WorkflowController::show()
50.4 执行工作流				POST /api/workflows/{id}/execute			WorkflowController::execute()
50.5 获取工作流执行状态		GET /api/workflows/executions/{execution_id} WorkflowController::getExecutionStatus()
50.6 提供步骤输入				POST /api/workflows/executions/{execution_id}/input WorkflowController::provideStepInput()
50.7 取消工作流执行			DELETE /api/workflows/executions/{execution_id} WorkflowController::cancelExecution()
50.8 获取工作流执行历史		GET /api/workflows/{id}/executions			WorkflowController::getExecutionHistory()

**UserGrowthController.php (10个接口)**
51.1 获取用户成长信息			GET /api/user-growth/profile				UserGrowthController::profile()
51.2 获取排行榜				GET /api/user-growth/leaderboard			UserGrowthController::leaderboard()
51.3 完成成就					POST /api/user-growth/complete-achievement	UserGrowthController::completeAchievement()
51.4 获取每日任务				GET /api/user-growth/daily-tasks			UserGrowthController::dailyTasks()
51.5 完成每日任务				POST /api/user-growth/complete-daily-task	UserGrowthController::completeDailyTask()
51.6 获取成长历史				GET /api/user-growth/history				UserGrowthController::history()
51.7 获取成长统计				GET /api/user-growth/statistics				UserGrowthController::statistics()
51.8 设置成长目标				POST /api/user-growth/set-goals				UserGrowthController::setGoals()
51.9 获取成长建议				GET /api/user-growth/recommendations		UserGrowthController::recommendations()
51.10 获取里程碑				GET /api/user-growth/milestones				UserGrowthController::milestones()
