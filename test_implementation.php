<?php

// 测试实施结果的简单脚本

echo "=== CogniDev 实施验证测试 ===\n\n";

// 检查文件是否存在
$files_to_check = [
    'app/Services/AiPlatformSelectionService.php',
    'app/Services/AiPlatformHealthService.php',
    'app/Services/AiLoadBalancingService.php',
    'app/Services/AiPlatformFallbackService.php',
    'app/Models/UserModelPreference.php',
    'app/Models/PlatformPerformanceMetric.php',
    'database/migrations/2025_01_01_000001_create_ai_model_configs_table.php',
    'database/migrations/2025_01_01_000002_create_user_model_preferences_table.php',
    'database/migrations/2025_01_01_000003_create_platform_performance_metrics_table.php',
    'database/seeders/AiModelConfigSeeder.php'
];

$created_files = 0;
$total_files = count($files_to_check);

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} - 已创建\n";
        $created_files++;
    } else {
        echo "❌ {$file} - 未找到\n";
    }
}

echo "\n=== 实施统计 ===\n";
echo "已创建文件: {$created_files}/{$total_files}\n";
echo "完成率: " . round(($created_files / $total_files) * 100, 1) . "%\n\n";

// 检查业务平台映射配置
echo "=== 业务平台映射配置验证 ===\n";
if (file_exists('app/Services/AiPlatformSelectionService.php')) {
    $content = file_get_contents('app/Services/AiPlatformSelectionService.php');
    
    // 检查关键配置
    $voice_config_correct = strpos($content, "'voice' => ['volcengine', 'minimax']") !== false;
    $sound_config_correct = strpos($content, "'sound' => ['volcengine', 'minimax']") !== false;
    $music_config_correct = strpos($content, "'music' => ['minimax']") !== false;
    
    echo "Voice业务配置: " . ($voice_config_correct ? "✅ 正确 (火山引擎豆包优先)" : "❌ 错误") . "\n";
    echo "Sound业务配置: " . ($sound_config_correct ? "✅ 正确 (火山引擎豆包优先)" : "❌ 错误") . "\n";
    echo "Music业务配置: " . ($music_config_correct ? "✅ 正确 (MiniMax唯一)" : "❌ 错误") . "\n";
} else {
    echo "❌ AiPlatformSelectionService.php 文件不存在，无法验证配置\n";
}

echo "\n=== 控制器增强验证 ===\n";
if (file_exists('app/Http/Controllers/Api/AiModelController.php')) {
    $content = file_get_contents('app/Http/Controllers/Api/AiModelController.php');
    
    $has_selection_method = strpos($content, 'selectOptimalPlatform') !== false;
    $has_health_method = strpos($content, 'checkPlatformHealth') !== false;
    $has_middleware = strpos($content, "middleware('auth:api')") !== false;
    
    echo "智能平台选择方法: " . ($has_selection_method ? "✅ 已添加" : "❌ 未添加") . "\n";
    echo "健康检查方法: " . ($has_health_method ? "✅ 已添加" : "❌ 未添加") . "\n";
    echo "中间件配置: " . ($has_middleware ? "✅ 已配置" : "❌ 未配置") . "\n";
} else {
    echo "❌ AiModelController.php 文件不存在，无法验证增强\n";
}

echo "\n=== 路由配置验证 ===\n";
if (file_exists('routes/web.php')) {
    $content = file_get_contents('routes/web.php');
    
    $has_select_route = strpos($content, 'select-platform') !== false;
    $has_health_route = strpos($content, 'platform-health') !== false;
    $has_stats_route = strpos($content, 'platform-stats') !== false;
    
    echo "平台选择路由: " . ($has_select_route ? "✅ 已添加" : "❌ 未添加") . "\n";
    echo "健康检查路由: " . ($has_health_route ? "✅ 已添加" : "❌ 未添加") . "\n";
    echo "统计数据路由: " . ($has_stats_route ? "✅ 已添加" : "❌ 未添加") . "\n";
} else {
    echo "❌ web.php 路由文件不存在，无法验证路由\n";
}

echo "\n=== 实施完成度评估 ===\n";

$implementation_score = 0;
$max_score = 100;

// 文件创建得分 (40分)
$file_score = ($created_files / $total_files) * 40;
$implementation_score += $file_score;

// 配置正确性得分 (30分)
$config_score = 0;
if (isset($voice_config_correct) && $voice_config_correct) $config_score += 10;
if (isset($sound_config_correct) && $sound_config_correct) $config_score += 10;
if (isset($music_config_correct) && $music_config_correct) $config_score += 10;
$implementation_score += $config_score;

// 功能增强得分 (30分)
$enhancement_score = 0;
if (isset($has_selection_method) && $has_selection_method) $enhancement_score += 10;
if (isset($has_health_method) && $has_health_method) $enhancement_score += 10;
if (isset($has_select_route) && $has_select_route) $enhancement_score += 10;
$implementation_score += $enhancement_score;

echo "文件创建得分: " . round($file_score, 1) . "/40\n";
echo "配置正确性得分: " . round($config_score, 1) . "/30\n";
echo "功能增强得分: " . round($enhancement_score, 1) . "/30\n";
echo "总体实施得分: " . round($implementation_score, 1) . "/100\n\n";

if ($implementation_score >= 90) {
    echo "🎉 实施质量: 优秀 (≥90分)\n";
    echo "✅ 战略蓝图已成功实施，可以提交审计\n";
} elseif ($implementation_score >= 80) {
    echo "👍 实施质量: 良好 (80-89分)\n";
    echo "⚠️ 大部分功能已实施，建议完善后提交审计\n";
} elseif ($implementation_score >= 70) {
    echo "⚠️ 实施质量: 一般 (70-79分)\n";
    echo "❌ 需要进一步完善后才能提交审计\n";
} else {
    echo "❌ 实施质量: 不合格 (<70分)\n";
    echo "❌ 实施不完整，需要重新实施\n";
}

echo "\n=== CogniDev 实施验证完成 ===\n";
