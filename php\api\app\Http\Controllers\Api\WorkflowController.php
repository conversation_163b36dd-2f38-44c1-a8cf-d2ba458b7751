<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\WorkflowService;
use Illuminate\Http\Request;

/**
 * 工作流管理控制器
 * 处理创作流程管理、任务流程、业务流程等
 */
class WorkflowController extends Controller
{
    protected $workflowService;

    public function __construct(WorkflowService $workflowService)
    {
        $this->workflowService = $workflowService;
    }

    /**
     * @ApiTitle("创建工作流")
     * @ApiSummary("创建新的工作流程")
     * @ApiMethod("POST")
     * @ApiRoute("/api/workflows")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="name", type="string", required=true, description="工作流名称")
     * @ApiParams(name="description", type="string", required=false, description="工作流描述")
     * @ApiParams(name="type", type="string", required=true, description="工作流类型：creation,approval,automation")
     * @ApiParams(name="steps", type="array", required=true, description="工作流步骤")
     * @ApiParams(name="triggers", type="array", required=false, description="触发条件")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "工作流创建成功",
     *   "data": {
     *     "id": 1,
     *     "name": "AI创作完整流程",
     *     "description": "从故事创作到视频生成的完整流程",
     *     "type": "creation",
     *     "status": "active",
     *     "steps": [
     *       {
     *         "id": 1,
     *         "name": "故事创作",
     *         "type": "ai_generation",
     *         "config": {"service": "story", "auto_proceed": true},
     *         "order": 1
     *       },
     *       {
     *         "id": 2,
     *         "name": "角色选择",
     *         "type": "user_input",
     *         "config": {"required": true, "timeout": 3600},
     *         "order": 2
     *       },
     *       {
     *         "id": 3,
     *         "name": "图像生成",
     *         "type": "ai_generation",
     *         "config": {"service": "image", "batch": true},
     *         "order": 3
     *       }
     *     ],
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function create(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'name' => 'required|string|max:200',
            'description' => 'sometimes|string|max:1000',
            'type' => 'required|string|in:creation,approval,automation,custom',
            'steps' => 'required|array|min:1|max:20',
            'steps.*.name' => 'required|string|max:100',
            'steps.*.type' => 'required|string|in:ai_generation,user_input,approval,automation,condition',
            'steps.*.config' => 'required|array',
            'steps.*.order' => 'required|integer|min:1',
            'triggers' => 'sometimes|array'
        ];

        $messages = [
            'name.required' => '工作流名称不能为空',
            'name.max' => '工作流名称不能超过200个字符',
            'type.required' => '工作流类型不能为空',
            'steps.required' => '工作流步骤不能为空',
            'steps.min' => '至少需要1个步骤',
            'steps.max' => '最多支持20个步骤',
            'steps.*.name.required' => '步骤名称不能为空',
            'steps.*.type.required' => '步骤类型不能为空',
            'steps.*.config.required' => '步骤配置不能为空',
            'steps.*.order.required' => '步骤顺序不能为空'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        $workflowData = [
            'name' => $request->name,
            'description' => $request->get('description', ''),
            'type' => $request->type,
            'steps' => $request->steps,
            'triggers' => $request->get('triggers', []),
            'created_by' => $user->id
        ];

        $result = $this->workflowService->createWorkflow($workflowData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("获取工作流列表")
     * @ApiSummary("查询工作流列表")
     * @ApiMethod("GET")
     * @ApiRoute("/api/workflows")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="工作流类型筛选")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选：active,inactive,draft")
     * @ApiParams(name="created_by", type="integer", required=false, description="创建者ID")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "workflows": [
     *       {
     *         "id": 1,
     *         "name": "AI创作完整流程",
     *         "description": "从故事创作到视频生成的完整流程",
     *         "type": "creation",
     *         "status": "active",
     *         "steps_count": 5,
     *         "executions_count": 150,
     *         "success_rate": 92.5,
     *         "created_by": "admin",
     *         "created_at": "2024-01-01 12:00:00",
     *         "updated_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 25,
     *       "last_page": 2
     *     }
     *   }
     * })
     */
    public function index(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'type' => 'sometimes|string|in:creation,approval,automation,custom',
            'status' => 'sometimes|string|in:active,inactive,draft',
            'created_by' => 'sometimes|integer|exists:users,id',
            'page' => 'sometimes|integer|min:1'
        ];

        $this->validateData($request->all(), $rules);

        $filters = [
            'type' => $request->get('type'),
            'status' => $request->get('status'),
            'created_by' => $request->get('created_by'),
            'page' => $request->get('page', 1),
            'per_page' => 20,
            'user_id' => $user->id,
            'is_admin' => $user->is_admin
        ];

        $result = $this->workflowService->getWorkflows($filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("获取工作流详情")
     * @ApiSummary("查询指定工作流的详细信息")
     * @ApiMethod("GET")
     * @ApiRoute("/api/workflows/{id}")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="工作流ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "name": "AI创作完整流程",
     *     "description": "从故事创作到视频生成的完整流程",
     *     "type": "creation",
     *     "status": "active",
     *     "steps": [
     *       {
     *         "id": 1,
     *         "name": "故事创作",
     *         "type": "ai_generation",
     *         "config": {
     *           "service": "story",
     *           "auto_proceed": true,
     *           "timeout": 300,
     *           "retry_count": 3
     *         },
     *         "order": 1,
     *         "conditions": [],
     *         "next_steps": [2]
     *       }
     *     ],
     *     "triggers": [
     *       {
     *         "type": "manual",
     *         "config": {"require_confirmation": true}
     *       }
     *     ],
     *     "statistics": {
     *       "total_executions": 150,
     *       "successful_executions": 139,
     *       "failed_executions": 11,
     *       "success_rate": 92.67,
     *       "avg_execution_time": 1800,
     *       "last_execution": "2024-01-01 12:00:00"
     *     },
     *     "created_by": "admin",
     *     "created_at": "2024-01-01 12:00:00",
     *     "updated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function show($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $result = $this->workflowService->getWorkflowDetail($id, $user->id, $user->is_admin);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("执行工作流")
     * @ApiSummary("启动工作流执行")
     * @ApiMethod("POST")
     * @ApiRoute("/api/workflows/{id}/execute")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="工作流ID")
     * @ApiParams(name="input_data", type="object", required=false, description="输入数据")
     * @ApiParams(name="priority", type="string", required=false, description="优先级：low,normal,high")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "工作流执行启动成功",
     *   "data": {
     *     "execution_id": "exec_123456",
     *     "workflow_id": 1,
     *     "workflow_name": "AI创作完整流程",
     *     "status": "running",
     *     "current_step": 1,
     *     "current_step_name": "故事创作",
     *     "progress": 0,
     *     "estimated_time": 1800,
     *     "started_at": "2024-01-01 12:00:00",
     *     "input_data": {
     *       "story_prompt": "一个关于友谊的温暖故事",
     *       "style": "cartoon"
     *     }
     *   }
     * })
     */
    public function execute($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'input_data' => 'sometimes|array',
            'priority' => 'sometimes|string|in:low,normal,high'
        ];

        $this->validateData($request->all(), $rules);

        $executionData = [
            'workflow_id' => $id,
            'input_data' => $request->get('input_data', []),
            'priority' => $request->get('priority', 'normal'),
            'executed_by' => $user->id
        ];

        $result = $this->workflowService->executeWorkflow($executionData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("获取工作流执行状态")
     * @ApiSummary("查询工作流执行状态")
     * @ApiMethod("GET")
     * @ApiRoute("/api/workflows/executions/{execution_id}")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="execution_id", type="string", required=true, description="执行ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "execution_id": "exec_123456",
     *     "workflow_id": 1,
     *     "workflow_name": "AI创作完整流程",
     *     "status": "running",
     *     "current_step": 2,
     *     "current_step_name": "角色选择",
     *     "progress": 40,
     *     "steps_status": [
     *       {
     *         "step_id": 1,
     *         "step_name": "故事创作",
     *         "status": "completed",
     *         "started_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:05:00",
     *         "output": {
     *           "story_id": 123,
     *           "story_content": "从前有一只小猫..."
     *         }
     *       },
     *       {
     *         "step_id": 2,
     *         "step_name": "角色选择",
     *         "status": "waiting_input",
     *         "started_at": "2024-01-01 12:05:00",
     *         "timeout_at": "2024-01-01 13:05:00",
     *         "required_input": {
     *           "character_id": "integer",
     *           "character_style": "string"
     *         }
     *       }
     *     ],
     *     "estimated_remaining_time": 1200,
     *     "started_at": "2024-01-01 12:00:00",
     *     "updated_at": "2024-01-01 12:05:00"
     *   }
     * })
     */
    public function getExecutionStatus($executionId, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $result = $this->workflowService->getExecutionStatus($executionId, $user->id, $user->is_admin);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("提供步骤输入")
     * @ApiSummary("为等待输入的步骤提供数据")
     * @ApiMethod("POST")
     * @ApiRoute("/api/workflows/executions/{execution_id}/input")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="execution_id", type="string", required=true, description="执行ID")
     * @ApiParams(name="step_id", type="integer", required=true, description="步骤ID")
     * @ApiParams(name="input_data", type="object", required=true, description="输入数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "输入数据提交成功，工作流继续执行",
     *   "data": {
     *     "execution_id": "exec_123456",
     *     "step_id": 2,
     *     "status": "processing",
     *     "next_step": 3,
     *     "updated_at": "2024-01-01 12:10:00"
     *   }
     * })
     */
    public function provideStepInput($executionId, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'step_id' => 'required|integer',
            'input_data' => 'required|array'
        ];

        $messages = [
            'step_id.required' => '步骤ID不能为空',
            'input_data.required' => '输入数据不能为空',
            'input_data.array' => '输入数据必须是对象格式'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        $inputData = [
            'execution_id' => $executionId,
            'step_id' => $request->step_id,
            'input_data' => $request->input_data,
            'provided_by' => $user->id
        ];

        $result = $this->workflowService->provideStepInput($inputData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("取消工作流执行")
     * @ApiSummary("取消正在执行的工作流")
     * @ApiMethod("DELETE")
     * @ApiRoute("/api/workflows/executions/{execution_id}")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="execution_id", type="string", required=true, description="执行ID")
     * @ApiParams(name="reason", type="string", required=false, description="取消原因")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "工作流执行取消成功",
     *   "data": {
     *     "execution_id": "exec_123456",
     *     "status": "cancelled",
     *     "cancelled_by": "user123",
     *     "cancelled_at": "2024-01-01 12:15:00",
     *     "reason": "用户主动取消",
     *     "completed_steps": 2,
     *     "total_steps": 5
     *   }
     * })
     */
    public function cancelExecution($executionId, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'reason' => 'sometimes|string|max:500'
        ];

        $this->validateData($request->all(), $rules);

        $cancelData = [
            'execution_id' => $executionId,
            'reason' => $request->get('reason', ''),
            'cancelled_by' => $user->id
        ];

        $result = $this->workflowService->cancelExecution($cancelData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle("获取工作流执行历史")
     * @ApiSummary("查询工作流的执行历史记录")
     * @ApiMethod("GET")
     * @ApiRoute("/api/workflows/{id}/executions")
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="工作流ID")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选：running,completed,failed,cancelled")
     * @ApiParams(name="start_date", type="string", required=false, description="开始日期")
     * @ApiParams(name="end_date", type="string", required=false, description="结束日期")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "workflow_id": 1,
     *     "workflow_name": "AI创作完整流程",
     *     "executions": [
     *       {
     *         "execution_id": "exec_123456",
     *         "status": "completed",
     *         "progress": 100,
     *         "executed_by": "user123",
     *         "started_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:30:00",
     *         "execution_time": 1800,
     *         "steps_completed": 5,
     *         "steps_total": 5,
     *         "success": true
     *       }
     *     ],
     *     "statistics": {
     *       "total_executions": 150,
     *       "success_rate": 92.67,
     *       "avg_execution_time": 1650,
     *       "fastest_execution": 1200,
     *       "slowest_execution": 3600
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 150,
     *       "last_page": 8
     *     }
     *   }
     * })
     */
    public function getExecutionHistory($id, Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'status' => 'sometimes|string|in:running,completed,failed,cancelled',
            'start_date' => 'sometimes|date_format:Y-m-d',
            'end_date' => 'sometimes|date_format:Y-m-d|after:start_date',
            'page' => 'sometimes|integer|min:1'
        ];

        $this->validateData($request->all(), $rules);

        $filters = [
            'workflow_id' => $id,
            'status' => $request->get('status'),
            'start_date' => $request->get('start_date'),
            'end_date' => $request->get('end_date'),
            'page' => $request->get('page', 1),
            'per_page' => 20,
            'user_id' => $user->id,
            'is_admin' => $user->is_admin
        ];

        $result = $this->workflowService->getExecutionHistory($filters);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
